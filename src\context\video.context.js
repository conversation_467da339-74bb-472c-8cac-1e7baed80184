"use client";
import React, {
    createContext,
    useContext,
    useState,
    useRef,
    useEffect,
    useCallback,
} from "react";
import { getLectureById } from "../services/indexedDB.service";
import {
    addToFavourite,
    removeFromFavourite,
} from "../services/favouriteLecture.service";
import {
    markAsCompleted,
    removeFromCompleted,
} from "../services/lectureCompletion.service";
import { updateLastPlayedPoint } from "../services/videoPlayerServices/videoPlayerActivity.service";
import {
    updateTopLecturesOnPlay,
    updateTopLecturesTime,
} from "../services/videoPlayerServices/topLecturesUpdate.service";
import { broadcastFavoriteChange, useFavoriteSync } from "../hooks/useFavoriteSync";

const VideoContext = createContext();

export const VideoContextProvider = ({ children }) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(1);
    const [playbackRate, setPlaybackRate] = useState(1);
    const [showVideoPlayer, setShowVideoPlayer] = useState(false);
    const [isFavorite, setIsFavorite] = useState(false);
    const [isCompleted, setIsCompleted] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [showControls, setShowControls] = useState(true);
    const [currentVideo, setCurrentVideo] = useState({
        title: "",
        subtitle: "",
        videoSrc: "",
        audioSrc: "",
        thumbnailUrl: "",
        id: null,
        description: "",
        dateOfRecording: "",
        place: "",
        category: "",
        language: "",
        totallength: 0,
    });
    const [startingTime, setStartingTime] = useState(0);
    const [shouldSeekToStartingTime, setShouldSeekToStartingTime] =
        useState(false);
    const [relatedVideos, setRelatedVideos] = useState([]);

    const videoRef = useRef(null);
    const trackingIntervalRef = useRef(null);
    const lastTrackingTimeRef = useRef(0);
    const controlsTimeoutRef = useRef(null);

    // YouTube player specific refs
    const youtubePlayerRef = useRef(null);
    const youtubeTrackingIntervalRef = useRef(null);
    const youtubeLastTrackingTimeRef = useRef(0);
    const isYouTubePlayerRef = useRef(false);

    // YouTube player utility functions
    const getYoutubeVideoId = (url) => {
        const regExp =
            /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return match && match[2].length === 11 ? match[2] : null;
    };

    const isYouTubeVideo = (videoSrc) => {
        return videoSrc && getYoutubeVideoId(videoSrc) !== null;
    };

    // YouTube tracking functions
    const startYouTubeTracking = () => {
        if (youtubeTrackingIntervalRef.current) {
            clearInterval(youtubeTrackingIntervalRef.current);
        }

        if (currentVideo.id && isPlaying) {
            youtubeLastTrackingTimeRef.current = Date.now();
            const intervalDuration = 60000 / playbackRate;

            youtubeTrackingIntervalRef.current = setInterval(async () => {
                try {
                    const videoSecondsConsumed = 60;
                    await updateLastPlayedPoint(
                        currentVideo.id,
                        videoSecondsConsumed
                    );

                    // Update top lectures video time tracking
                    await updateTopLecturesTime();

                    youtubeLastTrackingTimeRef.current = Date.now();
                } catch (error) {
                    console.error(
                        "Error updating YouTube video tracking:",
                        error
                    );
                }
            }, intervalDuration);
        }
    };

    const stopYouTubeTracking = () => {
        if (youtubeTrackingIntervalRef.current) {
            clearInterval(youtubeTrackingIntervalRef.current);
            youtubeTrackingIntervalRef.current = null;
        }
    };

    // Initialize YouTube player
    const initializeYouTubePlayer = (videoId) => {
        if (!window.YT) {
            const tag = document.createElement("script");
            tag.src = "https://www.youtube.com/iframe_api";
            document.body.appendChild(tag);
        }

        // Determine which player element to use (desktop or mobile)
        const getPlayerElementId = () => {
            const desktopElement = document.getElementById("youtube-player");
            const mobileElement = document.getElementById(
                "youtube-player-mobile"
            );

            if (desktopElement) return "youtube-player";
            if (mobileElement) return "youtube-player-mobile";
            return "youtube-player"; // fallback
        };

        window.onYouTubeIframeAPIReady = () => {
            const playerId = getPlayerElementId();
            youtubePlayerRef.current = new window.YT.Player(playerId, {
                height: "100%",
                width: "100%",
                videoId,
                playerVars: {
                    rel: 0,
                    modestbranding: 1,
                    autoplay: 1,
                    controls: 1,
                },
                events: {
                    onStateChange: handleYouTubeStateChange,
                    onReady: handleYouTubeReady,
                },
            });
        };

        // If YT is already loaded, initialize immediately
        if (window.YT && window.YT.Player) {
            window.onYouTubeIframeAPIReady();
        }
    };

    const handleYouTubeReady = (event) => {
        // Seek to starting time if needed
        if (shouldSeekToStartingTime && startingTime > 0) {
            const player = event.target;
            const duration = player.getDuration();
            const calculatedStartTime =
                startingTime >= duration ? 0 : startingTime;

            console.log(
                `YouTube player ready - Duration: ${duration}s, LastPlayedPoint: ${startingTime}s, Starting from: ${calculatedStartTime}s`
            );

            if (calculatedStartTime > 0) {
                player.seekTo(calculatedStartTime, true);
                setCurrentTime(calculatedStartTime);
            }
            setShouldSeekToStartingTime(false);
        }
    };

    const handleYouTubeStateChange = (event) => {
        const playerState = event.data;

        if (playerState === window.YT.PlayerState.PLAYING) {
            setIsPlaying(true);
            startYouTubeTracking();
            // Start updating current time for YouTube player
            startYouTubeTimeUpdate();
        } else if (playerState === window.YT.PlayerState.PAUSED) {
            setIsPlaying(false);
            stopYouTubeTracking();
            stopYouTubeTimeUpdate();
        } else if (playerState === window.YT.PlayerState.ENDED) {
            setIsPlaying(false);
            stopYouTubeTracking();
            stopYouTubeTimeUpdate();
            handleYouTubeVideoEnd();
        }
    };

    // YouTube time update functions
    const youtubeTimeUpdateIntervalRef = useRef(null);

    const startYouTubeTimeUpdate = () => {
        if (youtubeTimeUpdateIntervalRef.current) {
            clearInterval(youtubeTimeUpdateIntervalRef.current);
        }

        youtubeTimeUpdateIntervalRef.current = setInterval(() => {
            if (
                youtubePlayerRef.current &&
                youtubePlayerRef.current.getCurrentTime
            ) {
                const currentTime = youtubePlayerRef.current.getCurrentTime();
                const duration = youtubePlayerRef.current.getDuration();
                setCurrentTime(currentTime);
                if (duration && duration !== duration) {
                    // Check if duration is valid
                    setDuration(duration);
                }
            }
        }, 1000);
    };

    const stopYouTubeTimeUpdate = () => {
        if (youtubeTimeUpdateIntervalRef.current) {
            clearInterval(youtubeTimeUpdateIntervalRef.current);
            youtubeTimeUpdateIntervalRef.current = null;
        }
    };

    const handleYouTubeVideoEnd = async () => {
        if (currentVideo.id) {
            const timeSinceLastTracking =
                Date.now() - youtubeLastTrackingTimeRef.current;
            const realTimeSeconds = Math.floor(timeSinceLastTracking / 1000);
            const videoContentSeconds = Math.floor(
                realTimeSeconds * playbackRate
            );
            const thresholdSeconds = 60;

            if (
                videoContentSeconds > 0 &&
                videoContentSeconds < thresholdSeconds
            ) {
                try {
                    await updateLastPlayedPoint(
                        currentVideo.id,
                        videoContentSeconds
                    );
                } catch (error) {
                    console.error(
                        "Error updating final played point for YouTube:",
                        error
                    );
                }
            }
        }
    };

    // Update current time while playing
    useEffect(() => {
        const updateTime = () => {
            if (videoRef.current) {
                setCurrentTime(videoRef.current.currentTime);
            }
        };

        if (videoRef.current) {
            videoRef.current.addEventListener("timeupdate", updateTime);
            videoRef.current.addEventListener("loadedmetadata", () => {
                setDuration(videoRef.current.duration);

                // Seek to starting time if needed
                if (shouldSeekToStartingTime && startingTime > 0) {
                    const calculatedStartTime =
                        startingTime >= videoRef.current.duration
                            ? 0
                            : startingTime;
                    console.log(
                        `Video loaded - Duration: ${videoRef.current.duration}s, LastPlayedPoint: ${startingTime}s, Starting from: ${calculatedStartTime}s`
                    );

                    if (calculatedStartTime > 0) {
                        videoRef.current.currentTime = calculatedStartTime;
                        setCurrentTime(calculatedStartTime);
                    }
                    setShouldSeekToStartingTime(false);
                } else if (shouldSeekToStartingTime) {
                    console.log(
                        `Video loaded - Starting from beginning (lastPlayedPoint: ${startingTime}s)`
                    );
                    setShouldSeekToStartingTime(false);
                }
            });
        }

        return () => {
            if (videoRef.current) {
                videoRef.current.removeEventListener("timeupdate", updateTime);
            }
        };
    }, [videoRef.current, shouldSeekToStartingTime, startingTime]);

    // Reset seeking state when video source changes
    useEffect(() => {
        if (currentVideo.videoSrc) {
            setCurrentTime(0);
        }
    }, [currentVideo.videoSrc]);

    // Play/Pause control
    useEffect(() => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.play().catch((error) => {
                    console.error("Error playing video:", error);
                    setIsPlaying(false);
                });
            } else {
                videoRef.current.pause();
            }
        }
    }, [isPlaying]);

    // Volume control
    useEffect(() => {
        if (videoRef.current) {
            videoRef.current.volume = volume;
        }
    }, [volume]);

    // Playback rate control
    useEffect(() => {
        if (videoRef.current) {
            videoRef.current.playbackRate = playbackRate;
        }
    }, [playbackRate]);

    // Auto-hide controls
    useEffect(() => {
        const resetControlsTimeout = () => {
            if (controlsTimeoutRef.current) {
                clearTimeout(controlsTimeoutRef.current);
            }

            if (isPlaying && !isFullscreen) {
                controlsTimeoutRef.current = setTimeout(() => {
                    setShowControls(false);
                }, 3000);
            }
        };

        if (isPlaying) {
            resetControlsTimeout();
        } else {
            setShowControls(true);
            if (controlsTimeoutRef.current) {
                clearTimeout(controlsTimeoutRef.current);
            }
        }

        return () => {
            if (controlsTimeoutRef.current) {
                clearTimeout(controlsTimeoutRef.current);
            }
        };
    }, [isPlaying, isFullscreen]);

    // Video tracking - Update lastPlayedPoint and top lectures every 60 seconds while playing
    useEffect(() => {
        // Check if this is a YouTube video
        const isYouTube = isYouTubeVideo(currentVideo.videoSrc);
        isYouTubePlayerRef.current = isYouTube;

        if (isYouTube) {
            // For YouTube videos, tracking is handled by YouTube player state changes
            // Stop regular tracking if it's running
            if (trackingIntervalRef.current) {
                clearInterval(trackingIntervalRef.current);
                trackingIntervalRef.current = null;
            }

            // Initialize YouTube player if video source is available
            if (currentVideo.videoSrc) {
                const videoId = getYoutubeVideoId(currentVideo.videoSrc);
                if (videoId) {
                    initializeYouTubePlayer(videoId);
                }
            }
        } else {
            // Regular video tracking for non-YouTube videos
            const startTracking = () => {
                if (trackingIntervalRef.current) {
                    clearInterval(trackingIntervalRef.current);
                }

                if (currentVideo.id && isPlaying) {
                    lastTrackingTimeRef.current = Date.now();
                    const intervalDuration = 60000 / playbackRate;

                    trackingIntervalRef.current = setInterval(async () => {
                        try {
                            const videoSecondsConsumed = 60;
                            await updateLastPlayedPoint(
                                currentVideo.id,
                                videoSecondsConsumed
                            );

                            // Update top lectures video time tracking
                            await updateTopLecturesTime();

                            lastTrackingTimeRef.current = Date.now();
                        } catch (error) {
                            console.error(
                                "Error updating video tracking:",
                                error
                            );
                        }
                    }, intervalDuration);
                }
            };

            const stopTracking = () => {
                if (trackingIntervalRef.current) {
                    clearInterval(trackingIntervalRef.current);
                    trackingIntervalRef.current = null;
                }
            };

            if (isPlaying && currentVideo.id) {
                startTracking();
            } else {
                stopTracking();
            }

            return () => {
                stopTracking();
            };
        }

        return () => {
            // Cleanup for both regular and YouTube videos
            if (trackingIntervalRef.current) {
                clearInterval(trackingIntervalRef.current);
                trackingIntervalRef.current = null;
            }
            if (youtubeTrackingIntervalRef.current) {
                clearInterval(youtubeTrackingIntervalRef.current);
                youtubeTrackingIntervalRef.current = null;
            }
            if (youtubeTimeUpdateIntervalRef.current) {
                clearInterval(youtubeTimeUpdateIntervalRef.current);
                youtubeTimeUpdateIntervalRef.current = null;
            }
        };
    }, [isPlaying, currentVideo.id, currentVideo.videoSrc, playbackRate]);

    // Handle video completion (only for regular videos, YouTube handled separately)
    useEffect(() => {
        const handleVideoEnd = async () => {
            if (currentVideo.id && !isYouTubePlayerRef.current) {
                const timeSinceLastTracking =
                    Date.now() - lastTrackingTimeRef.current;
                const realTimeSeconds = Math.floor(
                    timeSinceLastTracking / 1000
                );
                const videoContentSeconds = Math.floor(
                    realTimeSeconds * playbackRate
                );
                const thresholdSeconds = 60;

                if (
                    videoContentSeconds > 0 &&
                    videoContentSeconds < thresholdSeconds
                ) {
                    try {
                        await updateLastPlayedPoint(
                            currentVideo.id,
                            videoContentSeconds
                        );
                    } catch (error) {
                        console.error(
                            "Error updating final played point:",
                            error
                        );
                    }
                }

                if (trackingIntervalRef.current) {
                    clearInterval(trackingIntervalRef.current);
                    trackingIntervalRef.current = null;
                }
            }
        };

        if (videoRef.current && !isYouTubePlayerRef.current) {
            videoRef.current.addEventListener("ended", handleVideoEnd);
        }

        return () => {
            if (videoRef.current) {
                videoRef.current.removeEventListener("ended", handleVideoEnd);
            }
        };
    }, [currentVideo.id, playbackRate]);

    // Check lecture status when video changes
    useEffect(() => {
        const checkLectureStatus = async () => {
            if (currentVideo.id) {
                try {
                    const lecture = await getLectureById(currentVideo.id);
                    if (lecture) {
                        if (lecture.isFavourite || lecture.favourite) {
                            setIsFavorite(true);
                        } else {
                            setIsFavorite(false);
                        }

                        if (lecture.isCompleted) {
                            setIsCompleted(true);
                        } else {
                            setIsCompleted(false);
                        }
                    }
                } catch (error) {
                    console.error("Error checking lecture status:", error);
                }
            }
        };

        checkLectureStatus();
    }, [currentVideo.id]);

    // Listen for favorite changes from other components (like lecture cards)
    useFavoriteSync(currentVideo.id, useCallback((lectureId, isFavoriteStatus) => {
        if (lectureId === currentVideo.id) {
            setIsFavorite(isFavoriteStatus);
        }
    }, [currentVideo.id]));

    const playVideo = async (videoData) => {
        try {
            if (videoData.id) {
                const lecture = await getLectureById(videoData.id);
                if (lecture && lecture.lastPlayedPoint) {
                    const startTimeInSeconds = lecture.lastPlayedPoint;
                    console.log(
                        `Playing video - Lecture ID: ${videoData.id}, LastPlayedPoint: ${lecture.lastPlayedPoint}s`
                    );
                    setStartingTime(startTimeInSeconds);
                    setShouldSeekToStartingTime(true);
                } else {
                    setStartingTime(0);
                    setShouldSeekToStartingTime(false);
                }
            } else {
                console.log(
                    `Playing video - No lecture ID provided, starting from beginning`
                );
                setStartingTime(0);
                setShouldSeekToStartingTime(false);
            }

            setCurrentVideo(videoData);
            setShowVideoPlayer(true);
            setIsPlaying(true);

            if (videoData.id) {
                try {
                    await updateTopLecturesOnPlay(videoData.id);
                } catch (error) {
                    console.error("Error updating TopLectures on play:", error);
                }
            }
        } catch (error) {
            console.error(
                "Error fetching lecture data for starting position:",
                error
            );
            setStartingTime(0);
            setShouldSeekToStartingTime(false);
            setCurrentVideo(videoData);
            setShowVideoPlayer(true);
            setIsPlaying(true);

            if (videoData.id) {
                try {
                    await updateTopLecturesOnPlay(videoData.id);
                } catch (topLectureError) {
                    console.error(
                        "Error updating TopLectures on play (fallback):",
                        topLectureError
                    );
                }
            }
        }
    };

    const togglePlay = () => {
        if (isYouTubePlayerRef.current && youtubePlayerRef.current) {
            // YouTube player toggle
            if (isPlaying) {
                youtubePlayerRef.current.pauseVideo();
            } else {
                youtubePlayerRef.current.playVideo();
            }
            // State will be updated by YouTube player state change handler
        } else {
            // Regular video toggle
            setIsPlaying(!isPlaying);
        }
    };

    const closeVideoPlayer = async () => {
        if (currentVideo.id) {
            // Handle YouTube video close
            if (
                isYouTubePlayerRef.current &&
                youtubeTrackingIntervalRef.current
            ) {
                const timeSinceLastTracking =
                    Date.now() - youtubeLastTrackingTimeRef.current;
                const realTimeSeconds = Math.floor(
                    timeSinceLastTracking / 1000
                );
                const videoContentSeconds = Math.floor(
                    realTimeSeconds * playbackRate
                );
                const thresholdSeconds = 60;

                if (
                    videoContentSeconds > 0 &&
                    videoContentSeconds < thresholdSeconds
                ) {
                    try {
                        await updateLastPlayedPoint(
                            currentVideo.id,
                            videoContentSeconds
                        );
                    } catch (error) {
                        console.error(
                            "Error updating final played point on close (YouTube):",
                            error
                        );
                    }
                }

                clearInterval(youtubeTrackingIntervalRef.current);
                youtubeTrackingIntervalRef.current = null;

                // Stop YouTube time updates
                stopYouTubeTimeUpdate();

                // Destroy YouTube player
                if (
                    youtubePlayerRef.current &&
                    youtubePlayerRef.current.destroy
                ) {
                    youtubePlayerRef.current.destroy();
                    youtubePlayerRef.current = null;
                }
            }
            // Handle regular video close
            else if (trackingIntervalRef.current) {
                const timeSinceLastTracking =
                    Date.now() - lastTrackingTimeRef.current;
                const realTimeSeconds = Math.floor(
                    timeSinceLastTracking / 1000
                );
                const videoContentSeconds = Math.floor(
                    realTimeSeconds * playbackRate
                );
                const thresholdSeconds = 60;

                if (
                    videoContentSeconds > 0 &&
                    videoContentSeconds < thresholdSeconds
                ) {
                    try {
                        await updateLastPlayedPoint(
                            currentVideo.id,
                            videoContentSeconds
                        );
                    } catch (error) {
                        console.error(
                            "Error updating final played point on close:",
                            error
                        );
                    }
                }

                clearInterval(trackingIntervalRef.current);
                trackingIntervalRef.current = null;
            }
        }

        setIsPlaying(false);
        setShowVideoPlayer(false);
        isYouTubePlayerRef.current = false;
    };

    const seekTo = (time) => {
        if (isYouTubePlayerRef.current && youtubePlayerRef.current) {
            // YouTube player seek
            youtubePlayerRef.current.seekTo(time, true);
            setCurrentTime(time);
            setShouldSeekToStartingTime(false);
        } else if (videoRef.current) {
            // Regular video seek
            videoRef.current.currentTime = time;
            setCurrentTime(time);
            setShouldSeekToStartingTime(false);
        }
    };

    const toggleFavorite = async () => {
        if (!currentVideo.id) return;

        try {
            setIsProcessing(true);
            const lectureId = parseInt(currentVideo.id);

            if (isFavorite) {
                const successfulIds = await removeFromFavourite(lectureId);

                console.log("toggleFavorite", typeof currentVideo.id);
                if (successfulIds.includes(lectureId)) {
                    setIsFavorite(false);
                    // Broadcast the favorite change to all listening components
                    broadcastFavoriteChange(lectureId, false);
                }
            } else {
                const successfulIds = await addToFavourite(lectureId);
                console.log("toggleFavorite", lectureId);
                if (successfulIds.includes(lectureId)) {
                    setIsFavorite(true);
                    // Broadcast the favorite change to all listening components
                    broadcastFavoriteChange(lectureId, true);
                }
            }
        } catch (error) {
            console.error("Error updating favorite status:", error);
        } finally {
            setIsProcessing(false);
        }
    };

    const toggleCompleted = async () => {
        if (!currentVideo.id) return;

        try {
            setIsProcessing(true);
            const lectureId = parseInt(currentVideo.id);

            if (isCompleted) {
                console.log("toggleCompleted", lectureId);
                const successfulIds = await removeFromCompleted(lectureId);
                console.log("toggleCompleted", typeof lectureId);

                if (successfulIds.includes(lectureId)) {
                    setIsCompleted(false);
                }
            } else {
                console.log("toggleCompleted", lectureId);
                const successfulIds = await markAsCompleted(lectureId);
                console.log("toggleCompleted", typeof currentVideo.id);
                if (successfulIds.includes(lectureId)) {
                    setIsCompleted(true);
                }
            }
        } catch (error) {
            console.error("Error updating completion status:", error);
        } finally {
            setIsProcessing(false);
        }
    };

    const toggleFullscreen = () => {
        if (!document.fullscreenElement) {
            if (videoRef.current?.requestFullscreen) {
                videoRef.current.requestFullscreen();
                setIsFullscreen(true);
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
                setIsFullscreen(false);
            }
        }
    };

    const showControlsTemporarily = () => {
        setShowControls(true);
        if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
        }

        if (isPlaying) {
            controlsTimeoutRef.current = setTimeout(() => {
                setShowControls(false);
            }, 3000);
        }
    };

    return (
        <VideoContext.Provider
            value={{
                isPlaying,
                setIsPlaying,
                currentTime,
                duration,
                volume,
                setVolume,
                playbackRate,
                setPlaybackRate,
                showVideoPlayer,
                setShowVideoPlayer,
                currentVideo,
                setCurrentVideo,
                videoRef,
                playVideo,
                togglePlay,
                closeVideoPlayer,
                seekTo,
                isFullscreen,
                setIsFullscreen,
                showControls,
                setShowControls,
                showControlsTemporarily,
                toggleFullscreen,
                relatedVideos,
                setRelatedVideos,
                isFavorite,
                isCompleted,
                isProcessing,
                toggleFavorite,
                toggleCompleted,
                isShareModalOpen,
                setIsShareModalOpen,
                // YouTube player utilities
                youtubePlayerRef,
                isYouTubeVideo,
                getYoutubeVideoId,
            }}
        >
            {children}
        </VideoContext.Provider>
    );
};

export const useVideoContext = () => {
    return useContext(VideoContext);
};
