import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "../config/firebase.config";
import { fetchUserStatistics } from "@/src/api/userStatistics.api";

// Interface for user statistics
export interface UserStatistics {
    bestStreak: number;
    currentStreak: number;
    lastUpdatedTime: number;

    // creationTimestamp?: number;
    // lastModifiedTimestamp?: number;
    // documentId?: string;
    // documentPath?: string;
}

/**
 * Get user statistics from Firebase
 * Creates default statistics if none exist
 *
 * @param userId - The user ID
 * @returns Promise<UserStatistics>
 */
export const getUserStatistics = async (
    userId: string
): Promise<UserStatistics> => {
    try {
        const docRef = doc(db, `users/${userId}/statistics/userStatistics`);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            return docSnap.data() as UserStatistics;
        } else {
            // Create default statistics
            const currentTimestamp = Date.now();
            const defaultStats: UserStatistics = {
                bestStreak: 1,
                currentStreak: 1,
                lastUpdatedTime: currentTimestamp,
            };

            await setDoc(docRef, defaultStats);
            return defaultStats;
        }
    } catch (error) {
        console.error("Error getting user statistics:", error);
        throw error;
    }
};

/**
 * Update user statistics with streak logic
 * Called every 60 seconds during audio/video playback
 *
 * @param userId - The user ID
 * @returns Promise<UserStatistics>
 */
export const updateUserStatistics = async (
    userId: string
): Promise<UserStatistics> => {
    try {
        const currentTimestamp = Date.now();
        const currentStats = await getUserStatistics(userId);

        // Get current date and last updated date
        const currentDate = new Date(currentTimestamp);
        const lastUpdatedDate = new Date(currentStats.lastUpdatedTime);

        // Reset time to start of day for accurate date comparison
        const currentDateOnly = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth(),
            currentDate.getDate()
        );
        const lastUpdatedDateOnly = new Date(
            lastUpdatedDate.getFullYear(),
            lastUpdatedDate.getMonth(),
            lastUpdatedDate.getDate()
        );

        // Calculate difference in days
        const daysDifference = Math.floor(
            (currentDateOnly.getTime() - lastUpdatedDateOnly.getTime()) /
                (1000 * 60 * 60 * 24)
        );

        let updatedStats: UserStatistics;

        if (daysDifference === 0) {
            // Same day - just update timestamp, no streak changes
            updatedStats = {
                ...currentStats,
                lastUpdatedTime: currentTimestamp,
            };
        } else if (daysDifference === 1) {
            // Next day - increment streak
            const newCurrentStreak = currentStats.currentStreak + 1;
            const newBestStreak = Math.max(
                currentStats.bestStreak,
                newCurrentStreak
            );

            updatedStats = {
                ...currentStats,
                currentStreak: newCurrentStreak,
                bestStreak: newBestStreak,
                lastUpdatedTime: currentTimestamp,
            };
        } else if (daysDifference > 1) {
            // Gap in days - reset current streak to 1
            updatedStats = {
                ...currentStats,
                currentStreak: 1,
                lastUpdatedTime: currentTimestamp,
            };
        } else {
            // daysDifference < 0 (shouldn't happen in normal cases, but handle it)
            // Just update timestamp
            updatedStats = {
                ...currentStats,
                lastUpdatedTime: currentTimestamp,
            };
        }

        // Save updated statistics to Firebase
        const docRef = doc(db, `users/${userId}/statistics/userStatistics`);
        await setDoc(docRef, updatedStats);
        fetchUserStatistics();

        return updatedStats;
    } catch (error) {
        console.error("Error updating user statistics:", error);
        throw error;
    }
};

/**
 * Public function to update user statistics
 * This can be called from audio/video services
 *
 * @returns Promise<UserStatistics | null>
 */
export const trackUserStatistics = async (): Promise<UserStatistics | null> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            console.warn("User not logged in - cannot track statistics");
            return null;
        }

        return await updateUserStatistics(userId);
    } catch (error) {
        console.error("Error tracking user statistics:", error);
        return null;
    }
};

/**
 * Reset user streak (for testing purposes or manual reset)
 *
 * @param userId - The user ID
 * @returns Promise<UserStatistics>
 */
export const resetUserStreak = async (
    userId: string
): Promise<UserStatistics> => {
    try {
        const currentTimestamp = Date.now();
        const resetStats: UserStatistics = {
            bestStreak: 1,
            currentStreak: 1,
            lastUpdatedTime: currentTimestamp,
        };

        const docRef = doc(db, `users/${userId}/statistics/userStatistics`);
        await setDoc(docRef, resetStats);

        return resetStats;
    } catch (error) {
        console.error("Error resetting user streak:", error);
        throw error;
    }
};

/**
 * Get formatted streak information for display
 *
 * @param userStats - User statistics object
 * @returns Object with formatted streak information
 */
export const getStreakInfo = (userStats: UserStatistics | null) => {
    if (!userStats) {
        return {
            currentStreak: 0,
            bestStreak: 0,
            isNewBest: false,
            lastUpdated: null,
        };
    }

    return {
        currentStreak: userStats.currentStreak,
        bestStreak: userStats.bestStreak,
        isNewBest:
            userStats.currentStreak === userStats.bestStreak &&
            userStats.currentStreak > 1,
        lastUpdated: new Date(userStats.lastUpdatedTime),
    };
};
