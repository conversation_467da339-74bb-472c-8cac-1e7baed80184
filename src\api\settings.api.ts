import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "../config/firebase.config";
import { fetch } from "../libs/helper";
import appConfig from "../config/apps";

export const fetchUserSettings = async (): Promise<any> => {
  const userId = localStorage.getItem("firebaseUid");
  if (!userId) {
    throw new Error("User ID not found in local storage");
  }

  try {
    const docRef = doc(db, `users/${userId}/Settings/userSettings`);
    const snapshot = await getDoc(docRef);
    const settings = snapshot.data();
    return settings;
  } catch (error) {
    console.error("Error in fetchSettings:", error);
    throw error;
  }
}

export const updateUserSettings = async (settings: any): Promise<void> => {
  const userId = localStorage.getItem("firebaseUid");
  if (!userId) {
    throw new Error("User ID not found in local storage");
  }

  try {
    const docRef = doc(db, `users/${userId}/Settings/userSettings`);
    await setDoc(docRef, settings, { merge: true });
  } catch (error) {
    console.error("Error updating user settings:", error);
    throw error;
  }
}

export const subscribeTopic = async (token: any, topic: any = appConfig.topics[0]) => {
    const res = await fetch({
        url: `/messaging/subscribe`,
        method: "POST",
        data: {
            token,
            topic,
        },
    });
    return res;
};

export const unsubscribeTopic = async (token: any, topic: any = appConfig.topics[0]) => {
    const res = await fetch({
        url: `/messaging/unsubscribe`,
        method: "POST",
        data: {
            token,
            topic,
        },
    });
    return res;
};