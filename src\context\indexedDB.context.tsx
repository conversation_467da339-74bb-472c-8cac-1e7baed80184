"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { initDB, getSyncInfo, getAllLectures, Lecture } from '../services/indexedDB.service';
import { syncLectures, syncLectureInfo } from '../services/sync.service';

interface IndexedDBContextType {
  isDBInitialized: boolean;
  lastSyncTime: number | null;
  totalLectures: number;
  syncData: () => Promise<void>;
  syncLectureInfoData: () => Promise<void>;
  isSyncing: boolean;
  getAllLectures: () => Promise<Lecture[]>;
}

const IndexedDBContext = createContext<IndexedDBContextType>({
  isDBInitialized: false,
  lastSyncTime: null,
  totalLectures: 0,
  syncData: async () => {},
  syncLectureInfoData: async () => {},
  isSyncing: false,
  getAllLectures: async () => [],
});

export const useIndexedDBContext = () => useContext(IndexedDBContext);

export const IndexedDBProvider = ({ children }: { children: ReactNode }) => {
  const [isDBInitialized, setIsDBInitialized] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
  const [totalLectures, setTotalLectures] = useState(0);
  const [isSyncing, setIsSyncing] = useState(false);

  // Initialize the database when the provider mounts
  useEffect(() => {
    const initializeDB = async () => {
      try {
        // Initialize the database
        await initDB();
        setIsDBInitialized(true);

        // Get sync info
        const syncInfo = await getSyncInfo();
        if (syncInfo) {
          setLastSyncTime(syncInfo.lastSyncTimestamp);
          setTotalLectures(syncInfo.totalLectures);
        }
      } catch (error) {
        console.error('Error initializing IndexedDB:', error);
      }
    };

    // Only run in browser environment
    if (typeof window !== 'undefined') {
      initializeDB();
    }
  }, []);

  // Function to trigger data synchronization
  const syncData = async () => {
    if (isSyncing) return;

    try {
      setIsSyncing(true);
      await syncLectures();

      // Update sync info after sync
      const syncInfo = await getSyncInfo();
      if (syncInfo) {
        setLastSyncTime(syncInfo.lastSyncTimestamp);
        setTotalLectures(syncInfo.totalLectures);
      }
    } catch (error) {
      console.error('Error syncing data:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  // Function to trigger only lectureInfo synchronization
  const syncLectureInfoData = async () => {
    if (isSyncing) return;

    try {
      setIsSyncing(true);
      await syncLectureInfo();

      // Update sync info after sync
      const syncInfo = await getSyncInfo();
      if (syncInfo) {
        setLastSyncTime(syncInfo.lastSyncTimestamp);
        setTotalLectures(syncInfo.totalLectures);
      }
    } catch (error) {
      console.error('Error syncing lectureInfo data:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <IndexedDBContext.Provider
      value={{
        isDBInitialized,
        lastSyncTime,
        totalLectures,
        syncData,
        syncLectureInfoData,
        isSyncing,
        getAllLectures,
      }}
    >
      {children}
    </IndexedDBContext.Provider>
  );
};
