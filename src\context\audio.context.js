"use client";
import React, {
    createContext,
    useContext,
    useState,
    useRef,
    useEffect,
    useCallback,
} from "react";
import { getLectureById } from "../services/indexedDB.service";
import {
    addToFavourite,
    removeFromFavourite,
} from "../services/favouriteLecture.service";
import {
    markAsCompleted,
    removeFromCompleted,
} from "../services/lectureCompletion.service";
import { updateLastPlayedPoint } from "../services/audioPlayerServices/audioPlayerActivity.service";
import {
    updateTopLecturesOnPlay,
    updateTopLecturesTime,
} from "../services/audioPlayerServices/topLecturesUpdate.service";
import { broadcastFavoriteChange, useFavoriteSync } from "../hooks/useFavoriteSync";
import { message } from "antd";
import { Poppins } from "next/font/google";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const AudioContext = createContext();

export const AudioContextProvider = ({ children }) => {
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(1);
    const [playbackRate, setPlaybackRate] = useState(1);
    const [showPlayer, setShowPlayer] = useState(false);
    const [isFavorite, setIsFavorite] = useState(false);
    const [isCompleted, setIsCompleted] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [currentAudio, setCurrentAudio] = useState({
        title: "",
        subtitle: "",
        audioSrc: "",
        thumbnailUrl: "",
        id: null,
    });
    const [highlightedSections, setHighlightedSections] = useState([]);
    const [startingTime, setStartingTime] = useState(0);
    const [shouldSeekToStartingTime, setShouldSeekToStartingTime] =
        useState(false);

    const audioRef = useRef(null);
    const trackingIntervalRef = useRef(null);
    const lastTrackingTimeRef = useRef(0);

    // Update current time while playing
    useEffect(() => {
        const updateTime = () => {
            if (audioRef.current) {
                setCurrentTime(audioRef.current.currentTime);
            }
        };

        if (audioRef.current) {
            audioRef.current.addEventListener("timeupdate", updateTime);
            audioRef.current.addEventListener("loadedmetadata", () => {
                setDuration(audioRef.current.duration);

                // Seek to starting time if needed
                if (shouldSeekToStartingTime && startingTime > 0) {
                    // startingTime is already in seconds, check if it's within audio duration
                    const calculatedStartTime =
                        startingTime >= audioRef.current.duration
                            ? 0
                            : startingTime;
                    console.log(
                        `Audio loaded - Duration: ${audioRef.current.duration}s, LastPlayedPoint: ${startingTime}s, Starting from: ${calculatedStartTime}s`
                    );

                    if (calculatedStartTime > 0) {
                        audioRef.current.currentTime = calculatedStartTime;
                        setCurrentTime(calculatedStartTime);
                    }
                    setShouldSeekToStartingTime(false);
                } else if (shouldSeekToStartingTime) {
                    console.log(
                        `Audio loaded - Starting from beginning (lastPlayedPoint: ${startingTime}s)`
                    );
                    setShouldSeekToStartingTime(false);
                }
            });
        }

        return () => {
            if (audioRef.current) {
                audioRef.current.removeEventListener("timeupdate", updateTime);
            }
        };
    }, [audioRef.current, shouldSeekToStartingTime, startingTime]);

    // Reset seeking state when audio source changes
    useEffect(() => {
        if (currentAudio.audioSrc) {
            // Reset current time when new audio is loaded
            setCurrentTime(0);
        }
    }, [currentAudio.audioSrc]);

    // Play/Pause control
    useEffect(() => {
        if (audioRef.current) {
            if (isPlaying) {
                audioRef.current.play().catch((error) => {
                    console.error("Error playing audio:", error);
                    setIsPlaying(false);
                });
            } else {
                audioRef.current.pause();
            }
        }
    }, [isPlaying, currentAudio.audioSrc]);

    // Volume control
    useEffect(() => {
        if (audioRef.current) {
            audioRef.current.volume = volume;
        }
    }, [volume]);

    // Playback rate control
    useEffect(() => {
        if (audioRef.current) {
            audioRef.current.playbackRate = playbackRate;
        }
    }, [playbackRate]);

    // Audio tracking - Update lastPlayedPoint every 60 seconds while playing (adjusted for playback speed)
    useEffect(() => {
        const startTracking = () => {
            if (trackingIntervalRef.current) {
                clearInterval(trackingIntervalRef.current);
            }

            if (currentAudio.id && isPlaying) {
                // Reset tracking time when starting
                lastTrackingTimeRef.current = Date.now();

                // Calculate interval based on playback rate
                // If playback rate is 2x, we want to track every 30 seconds (60/2)
                // If playback rate is 0.5x, we want to track every 120 seconds (60/0.5)
                const intervalDuration = 60000 / playbackRate; // 60 seconds adjusted for speed

                trackingIntervalRef.current = setInterval(async () => {
                    try {
                        // Always track 60 seconds of audio content
                        const audioSecondsConsumed = 60;
                        await updateLastPlayedPoint(
                            currentAudio.id,
                            audioSecondsConsumed
                        );

                        // Update TopLectures time tracking every 60 seconds
                        await updateTopLecturesTime();
                        // Note: updateLastPlayedPoint already handles listening activity tracking internally
                        lastTrackingTimeRef.current = Date.now();
                    } catch (error) {
                        console.error("Error updating tracking data:", error);
                    }
                }, intervalDuration);
            }
        };

        const stopTracking = () => {
            if (trackingIntervalRef.current) {
                clearInterval(trackingIntervalRef.current);
                trackingIntervalRef.current = null;
            }
        };

        if (isPlaying && currentAudio.id) {
            startTracking();
        } else {
            stopTracking();
        }

        return () => {
            stopTracking();
        };
    }, [isPlaying, currentAudio.id, playbackRate]);

    // Handle audio completion - update remaining seconds (adjusted for playback speed)
    useEffect(() => {
        const handleAudioEnd = async () => {
            if (currentAudio.id) {
                // Calculate remaining real-time seconds since last tracking
                const timeSinceLastTracking =
                    Date.now() - lastTrackingTimeRef.current;
                const realTimeSeconds = Math.floor(
                    timeSinceLastTracking / 1000
                );

                // Calculate audio content seconds based on playback rate
                const audioContentSeconds = Math.floor(
                    realTimeSeconds * playbackRate
                );

                // Calculate the threshold based on playback rate (60 seconds of audio content)
                const thresholdSeconds = 60;

                // If there are remaining audio content seconds (less than 60 seconds of audio), update them
                if (
                    audioContentSeconds > 0 &&
                    audioContentSeconds < thresholdSeconds
                ) {
                    try {
                        // Update lastPlayedPoint with remaining audio content seconds
                        await updateLastPlayedPoint(
                            currentAudio.id,
                            audioContentSeconds
                        );
                        // Note: updateLastPlayedPoint already handles listening activity tracking internally
                    } catch (error) {
                        console.error(
                            "Error updating final played point:",
                            error
                        );
                    }
                }

                // Clear the tracking interval if it exists
                if (trackingIntervalRef.current) {
                    clearInterval(trackingIntervalRef.current);
                    trackingIntervalRef.current = null;
                }
            }
        };

        if (audioRef.current) {
            audioRef.current.addEventListener("ended", handleAudioEnd);
        }

        return () => {
            if (audioRef.current) {
                audioRef.current.removeEventListener("ended", handleAudioEnd);
            }
        };
    }, [currentAudio.id, playbackRate]);

    // Check lecture status when audio changes
    useEffect(() => {
        const checkLectureStatus = async () => {
            if (currentAudio.id) {
                try {
                    const lecture = await getLectureById(currentAudio.id);
                    if (lecture) {
                        // Check favorite status
                        if (lecture.isFavourite || lecture.favourite) {
                            setIsFavorite(true);
                        } else {
                            setIsFavorite(false);
                        }

                        // Check completion status
                        if (lecture.isCompleted) {
                            setIsCompleted(true);
                        } else {
                            setIsCompleted(false);
                        }
                    }
                } catch (error) {
                    console.error("Error checking lecture status:", error);
                }
            }
        };

        checkLectureStatus();
    }, [currentAudio.id]);

    // Listen for favorite changes from other components (like lecture cards)
    useFavoriteSync(currentAudio.id, useCallback((lectureId, isFavoriteStatus) => {
        if (lectureId === currentAudio.id) {
            setIsFavorite(isFavoriteStatus);
        }
    }, [currentAudio.id]));

    const playAudio = async (audioData) => {
        try {
            // Fetch lecture data to get lastPlayedPoint
            if (audioData.id) {
                const lecture = await getLectureById(audioData.id);
                if (lecture && lecture.lastPlayedPoint) {
                    // lastPlayedPoint is already in seconds, use it directly
                    const startTimeInSeconds = lecture.lastPlayedPoint;
                    console.log(
                        `Playing audio - Lecture ID: ${audioData.id}, LastPlayedPoint: ${lecture.lastPlayedPoint}s`
                    );
                    setStartingTime(startTimeInSeconds);
                    setShouldSeekToStartingTime(true);
                } else {
                    // No lastPlayedPoint, start from beginning
                    console.log(
                        `Playing audio - Lecture ID: ${audioData.id}, No lastPlayedPoint found, starting from beginning`
                    );
                    setStartingTime(0);
                    setShouldSeekToStartingTime(false);
                }
            } else {
                // No lecture ID, start from beginning
                console.log(
                    `Playing audio - No lecture ID provided, starting from beginning`
                );
                setStartingTime(0);
                setShouldSeekToStartingTime(false);
            }

            setCurrentAudio(audioData);
            setShowPlayer(true);
            setIsPlaying(true);

            // Update TopLectures collection when audio starts playing
            if (audioData.id) {
                try {
                    await updateTopLecturesOnPlay(audioData.id);
                } catch (error) {
                    console.error("Error updating TopLectures on play:", error);
                    // Don't throw error, just log it to avoid breaking audio playback
                }
            }
        } catch (error) {
            console.error(
                "Error fetching lecture data for starting position:",
                error
            );
            // Fallback: start from beginning
            setStartingTime(0);
            setShouldSeekToStartingTime(false);
            setCurrentAudio(audioData);
            setShowPlayer(true);
            setIsPlaying(true);

            // Update TopLectures collection when audio starts playing (fallback case)
            if (audioData.id) {
                try {
                    await updateTopLecturesOnPlay(audioData.id);
                } catch (topLectureError) {
                    console.error(
                        "Error updating TopLectures on play (fallback):",
                        topLectureError
                    );
                    // Don't throw error, just log it to avoid breaking audio playback
                }
            }
        }
    };

    const togglePlay = () => {
        setIsPlaying(!isPlaying);
    };

    const closePlayer = async () => {
        setIsPlaying(false);
        setShowPlayer(false);
        setStartingTime(0);

        // Handle remaining seconds when closing player (adjusted for playback speed)
        if (currentAudio.id && trackingIntervalRef.current) {
            const timeSinceLastTracking =
                Date.now() - lastTrackingTimeRef.current;
            const realTimeSeconds = Math.floor(timeSinceLastTracking / 1000);

            // Calculate audio content seconds based on playback rate
            const audioContentSeconds = Math.floor(
                realTimeSeconds * playbackRate
            );

            // Calculate the threshold based on playback rate (60 seconds of audio content)
            const thresholdSeconds = 60;

            // If there are remaining audio content seconds (less than 60 seconds of audio), update them
            if (
                audioContentSeconds > 0 &&
                audioContentSeconds < thresholdSeconds
            ) {
                try {
                    await updateLastPlayedPoint(
                        currentAudio.id,
                        audioContentSeconds
                    );
                    // Note: updateLastPlayedPoint already handles listening activity tracking internally
                } catch (error) {
                    console.error(
                        "Error updating final played point on close:",
                        error
                    );
                }
            }

            // Clear the tracking interval
            clearInterval(trackingIntervalRef.current);
            trackingIntervalRef.current = null;
        }

        // setIsPlaying(false);
        // setShowPlayer(false);
    };

    const seekTo = (time) => {
        if (audioRef.current) {
            audioRef.current.currentTime = time;
            setCurrentTime(time);
            // Reset starting time state since user manually seeked
            setShouldSeekToStartingTime(false);
        }
    };

    const toggleFavorite = async () => {
        if (!currentAudio.id) return;

        try {
            setIsProcessing(true);

            if (isFavorite) {
                // Remove from favorites
                const successfulIds = await removeFromFavourite(
                    currentAudio.id
                );
                if (successfulIds.includes(currentAudio.id)) {
                    setIsFavorite(false);
                    // Broadcast the favorite change to all listening components
                    broadcastFavoriteChange(currentAudio.id, false);
                    // message.success({
                    //     content: "Lecture successfully removed from favorites!",
                    //     className: `${poppins.className}`,
                    // });
                }
            } else {
                // Add to favorites
                const successfulIds = await addToFavourite(currentAudio.id);
                if (successfulIds.includes(currentAudio.id)) {
                    setIsFavorite(true);
                    // Broadcast the favorite change to all listening components
                    broadcastFavoriteChange(currentAudio.id, true);
                    // message.success({
                    //     content: "Lecture successfully Added to favorites!",
                    //     className: `${poppins.className}`,
                    // });
                }
            }
        } catch (error) {
            console.error("Error updating favorite status:", error);
        } finally {
            setIsProcessing(false);
        }
    };

    const toggleCompleted = async () => {
        if (!currentAudio.id) return;

        try {
            setIsProcessing(true);

            if (isCompleted) {
                // Remove from completed
                const successfulIds = await removeFromCompleted(
                    currentAudio.id
                );
                if (successfulIds.includes(currentAudio.id)) {
                    setIsCompleted(false);
                    // message.success({
                    //     content: "Completion Reset Successfully!",
                    //     className: `${poppins.className}`,
                    // });
                }
            } else {
                // Mark as completed
                const successfulIds = await markAsCompleted(currentAudio.id);
                if (successfulIds.includes(currentAudio.id)) {
                    setIsCompleted(true);
                    // message.success({
                    //     content: "Marked as completed!",
                    //     className: `${poppins.className}`,
                    // });
                }
            }
        } catch (error) {
            console.error("Error updating completion status:", error);
        } finally {
            setIsProcessing(false);
        }
    };

    const playLecture = async (lectureData) => {
        setShowPlayer(true);
        setIsPlaying(true);
        setCurrentAudio({
            title: Array.isArray(lectureData.title)
                ? lectureData.title.join(" ")
                : lectureData.title,
            subtitle: Array.isArray(lectureData.category)
                ? lectureData.category.join(", ")
                : lectureData.category,
            audioSrc:
                lectureData.resources?.audios?.[0]?.url || lectureData.audioUrl,
            thumbnailUrl: lectureData.thumbnail || lectureData.thumbnailUrl,
            id: lectureData.id,
        });

        if (lectureData.id) {
            // const lecture = await getLectureById(audioData.id);
            if (lectureData && lectureData.lastPlayedPoint) {
                // lastPlayedPoint is already in seconds, use it directly
                const startTimeInSeconds = lectureData.lastPlayedPoint;
                console.log(
                    `Playing audio - Lecture ID: ${lectureData.id}, LastPlayedPoint: ${lectureData.lastPlayedPoint}s`
                );
                setStartingTime(startTimeInSeconds);
                setShouldSeekToStartingTime(true);
            } else {
                // No lastPlayedPoint, start from beginning
                console.log(
                    `Playing audio - Lecture ID: ${lectureData.id}, No lastPlayedPoint found, starting from beginning`
                );
                setStartingTime(0);
                setShouldSeekToStartingTime(false);
            }
        } else {
            // No lecture ID, start from beginning
            console.log(
                `Playing audio - No lecture ID provided, starting from beginning`
            );
            setStartingTime(0);
            setShouldSeekToStartingTime(false);
        }

        // Update TopLectures collection when lecture starts playing
        if (lectureData.id) {
            try {
                await updateTopLecturesOnPlay(lectureData.id);
            } catch (error) {
                console.error(
                    "Error updating TopLectures on lecture play:",
                    error
                );
                // Don't throw error, just log it to avoid breaking audio playback
            }
        }
    };

    const updateHighlightedSections = useCallback((sections) => {
        setHighlightedSections(sections);
    }, []);

    return (
        <AudioContext.Provider
            value={{
                isPlaying,
                setIsPlaying,
                currentTime,
                duration,
                volume,
                setVolume,
                playbackRate,
                setPlaybackRate,
                showPlayer,
                setShowPlayer,
                currentAudio,
                setCurrentAudio,
                audioRef,
                playAudio,
                togglePlay,
                closePlayer,
                seekTo,
                isFavorite,
                isCompleted,
                isProcessing,
                toggleFavorite,
                toggleCompleted,
                isShareModalOpen,
                setIsShareModalOpen,
                playLecture,
                highlightedSections,
                updateHighlightedSections,
            }}
        >
            {children}
            {/* Audio element */}
            <audio
                ref={audioRef}
                src={currentAudio.audioSrc}
                onEnded={() => setIsPlaying(false)}
            />
        </AudioContext.Provider>
    );
};

export const useAudioContext = () => {
    return useContext(AudioContext);
};
