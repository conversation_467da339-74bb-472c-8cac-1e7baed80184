import Image from "next/image";
import React, { useEffect, useState } from "react";
import { Modal, Button, Input, Select, Segmented, message } from "antd";
import { Poppins } from "next/font/google";
import SearchIcon from "../ui/searchIcon";
import { PlaylistType } from "@/src/libs/constant";
import {
  addToPrivatePlaylist,
  addToPublicPlaylist,
  createPlaylist,
  fetchUserPrivatePlaylists,
  fetchUserPublicPlaylists,
} from "@/src/services/playlist.service";
import IconCheck from "../ui/iconCheck";
import AddToPlaylistModal from "../Modal/addToPlaylistModal";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const { TextArea } = Input;

const AddToPlaylist = ({ selectedFiles }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const handleOpenModal = () => {
    if (selectedFiles.length > 0) {
      setIsModalOpen(true);
    }
  };

  return (
    <div className={`${poppins.className}`}>
      <div
        className={`h-[36px] flex gap-2 items-center pt-[2px] px-1 md:px-3 text-[13px] text-left rounded-[12px] transition-all duration-300 ${
          selectedFiles.length > 0
            ? "opacity-100 cursor-pointer hover:bg-primary-hover"
            : "opacity-70 cursor-default"
        }`}
        onClick={handleOpenModal}
      >
        <Image
          src="/images/helperComponents/IconPlaylistAdd.svg"
          width={20}
          height={20}
          alt=""
          className={`w-[20px] h-[20px]`}
        />
        <h2
          className={`text-[14px] leading-5 font-[500] text-text-primary min-[924px]:block hidden `}
        >
          Add to Playlist
        </h2>
      </div>

      <AddToPlaylistModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        selectedFiles={selectedFiles}
      />
    </div>
  );
};

export default AddToPlaylist;
