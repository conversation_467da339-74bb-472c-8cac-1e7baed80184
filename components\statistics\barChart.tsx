"use client";
import React from "react";
import dynamic from "next/dynamic";
import { formatCategoryTimeHMS } from "@/src/utils/chartTimeFormat";
import { Zoom } from "@mui/material";

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface ChartDataItem {
  day: string;
  date: string | number;
  hours: number;
}

interface BarChartProps {
  data: ChartDataItem[];
  height?: number;
  activeTab?: string;
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  height = 200,
  activeTab = "Week",
}) => {
  // Get chart axis configuration with proper time formatting
  const maxHours = Math.max(...data.map((item) => item.hours));

  // Determine if we should use minutes or hours based on max value
  const shouldUseMinutes = maxHours < 1;

  const getMaxValue = () => {
    if (shouldUseMinutes) {
      const maxMinutes = Math.ceil(maxHours * 60);
      return Math.ceil(maxMinutes / 10) * 10; // Round to nearest 10 minutes
    } else {
      return Math.ceil(maxHours); // Round up to nearest hour
    }
  };

  const barChartOptions = {
    chart: {
      type: "bar" as const,
      height: height,
      toolbar: { show: false },
      background: "transparent",
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "20px",
        colors: {
          ranges: [
            {
              from: 0,
              to: 100,
              color: "#F9DFCA",
            },
          ],
        },
      },
    },
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
      active: {
        filter: {
          type: "none",
        },
      },
    },
    dataLabels: { enabled: false },
    stroke: { show: false },
    xaxis: {
      categories: data.map((item) => item),
      labels: {
        formatter: function (value: any, timestamp?: number, opts?: any) {
          const item = value as ChartDataItem;
          // For Week view, show day name and date
          if (activeTab === "Week") {
            return [String(item.date), item?.day?.toUpperCase()];
          }
          // For Month view, show only date
          else if (activeTab === "Month") {
            return [String(item.date)];
          }
          // For Year view, show month name
          else {
            return [item?.day?.toUpperCase()];
          }
        },
        style: {
          colors: "#959698",
          fontSize: "14px",
          fontFamily: "'Poppins', sans-serif",
        },
        rotate: 0,
        trim: false,
        hideOverlappingLabels: false,
      },
      axisBorder: { show: true },
      axisTicks: { show: false },
    },
    yaxis: {
      opposite: true,
      min: 0, // Always start Y-axis from 0 to prevent negative values
      max: shouldUseMinutes ? getMaxValue() / 60 : getMaxValue(),
      labels: {
        formatter: shouldUseMinutes
          ? (value: number) => `${Math.round(value * 60)}m`
          : (value: number) =>
              value % 1 === 0 ? `${value}h` : `${value.toFixed(1)}h`,
        style: {
          colors: "#959698",
          fontSize: "14px",
          fontFamily: "'Poppins', sans-serif",
        },
      },
    },
    grid: {
      borderColor: "#F3EFED",
      strokeDashArray: 0,
      xaxis: { lines: { show: false } },
      yaxis: { lines: { show: true } },
    },
    tooltip: {
      custom: function ({ dataPointIndex }: any) {
        const item = data[dataPointIndex];

        // Convert hours to seconds for h m s formatting
        const totalSeconds = Math.round(item.hours * 3600);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = Math.floor(totalSeconds % 60);

        // Format time in h m s format
        const parts: string[] = [];
        if (hours > 0) parts.push(`${hours}h`);
        if (minutes > 0) parts.push(`${minutes}m`);
        if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`);
        const timeDisplay = parts.join(" ");

        return `<div style="
                    color: #F97316;
                    padding: 8px 12px;
                    border-radius: 8px;
                    font-family: 'Poppins', sans-serif;
                    font-size: 16px;
                    font-weight: 600;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    position: relative;
                ">
                    ${timeDisplay}
                    <div style="
                        position: absolute;
                        bottom: -6px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 0;
                        height: 0;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        border-top: 6px solid #F97316;
                    "></div>
                </div>`;
      },
    },
    fill: {
      colors: ["#F9DFCA"],
    },
  };

  const barChartSeries = [
    {
      name: "Hours",
      data: data.map((item) => ({
        x: item.date.toString(),
        y: Math.max(0, item.hours || 0), // Ensure non-negative values
        fillColor: "#F9DFCA",
      })),
    },
  ];

  return (
    <div className="w-full">
      <style jsx global>{`
        .apexcharts-bar-area:hover {
          fill: #f97316 !important;
        }
        .apexcharts-series path:hover {
          fill: #f97316 !important;
        }
        .apexcharts-series rect:hover {
          fill: #f97316 !important;
        }
      `}</style>
      {typeof window !== "undefined" && (
        <Chart
          options={barChartOptions}
          series={barChartSeries}
          type="bar"
          height={height}
        />
      )}
    </div>
  );
};

export default BarChart;
