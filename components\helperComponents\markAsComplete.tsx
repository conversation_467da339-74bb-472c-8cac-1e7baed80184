import { markAsCompleted } from "@/src/services/lectureCompletion.service";
import { message, Spin } from "antd";
import Image from "next/image";
import React, { useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import IconCheck from "../ui/iconCheck";

const MarkAsComplete = ({ selectedFiles, onCompleteUpdated }: any) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleMarkAsCompleted = async () => {
    if (selectedFiles.length === 0) return;

    try {
      setIsProcessing(true);

      // Call the addToFavourite function with batch size
      const successfulIds = await markAsCompleted(selectedFiles);

      if (successfulIds.length > 0) {
        // message.success(`Added ${successfulIds.length} lecture(s) to favorites`);

        // Notify parent component about the updated favorites
        onCompleteUpdated(successfulIds, "isCompleted", true);
      } else {
        message.warning("No lectures were marked as completed.");
      }
       message.success("Lectures has been marked as Completed.");
    } catch (error) {
      console.error("Error marking as completed:", error);
      message.error("Failed to mark as completed.");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div
      className={`h-[36px] flex gap-2 items-center pt-[2px] px-1 md:px-3 text-[13px] text-left rounded-[12px] transition-all duration-300 ${
        selectedFiles.length > 0
          ? "opacity-100 cursor-pointer hover:bg-primary-hover"
          : "opacity-70 cursor-default"
      }`}
      onClick={handleMarkAsCompleted}
    >
      {isProcessing ? (
        <Spin
          indicator={<LoadingOutlined spin className="text-text-primary" />}
          size="small"
        />
      ) : (
        <IconCheck color="#343A40" />
      )}
      <h2
        className={`text-[14px] leading-5 font-[500] text-text-primary min-[924px]:block hidden`}
      >
        Mark as Complete
      </h2>
    </div>
  );
};

export default MarkAsComplete;
